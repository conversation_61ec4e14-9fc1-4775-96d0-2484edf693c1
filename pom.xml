<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.1</version>
		<relativePath/>
	</parent>

	<groupId>com.kering.cus</groupId>
	<artifactId>cus-sale-biz-remote-sales</artifactId>
	<name>cus-sale-biz-remote-sales</name>
	<version>1.0.0-SNAPSHOT</version>
	<description>the sample service for quickly build a microservice application</description>

	<properties>
		<maven.compiler.source>21</maven.compiler.source>
		<maven.compiler.target>21</maven.compiler.target>
		<maven-compiler-plugin>3.12.1</maven-compiler-plugin>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<cus-lib.version>1.32.1</cus-lib.version>
		<sonar-plugin.version>3.10.0.2594</sonar-plugin.version>
		<jacoco-plugin.version>0.8.12</jacoco-plugin.version>
		<spotbugs-plugin.version>*******</spotbugs-plugin.version>
		<findsecbugs-plugin.version>1.12.0</findsecbugs-plugin.version>
		<mysql-connector.version>8.2.0</mysql-connector.version>
		<jedis.version>5.1.0</jedis.version>
		<jackson-datatype.version>2.13.0</jackson-datatype.version>
		<mockito.version>5.12.0</mockito.version>
		<spring.kafka.version>3.0.11</spring.kafka.version>
		<spring.kafka.client.version>3.7.1</spring.kafka.client.version>
		<sonar.coverage.exclusions>
			/src/main/java/**/dto/*.java,
			/src/main/java/**/dao/*.java,
			/src/main/java/**/mapper/*.java,
			/src/main/java/**/entity/*.java,
			/src/main/java/**/config/*.java,
			/src/main/java/**/client/**/*.java,
			/src/main/java/**/converter/*.java,
			/src/main/java/**/exception/*.java,
			/src/main/java/**/constant/*.java
		</sonar.coverage.exclusions>
		<sonar.issue.ignore.multicriteria>e1,e2,e3</sonar.issue.ignore.multicriteria>
		<sonar.issue.ignore.multicriteria.e1.ruleKey>java:S6813</sonar.issue.ignore.multicriteria.e1.ruleKey>
		<sonar.issue.ignore.multicriteria.e1.resourceKey>**/*.java</sonar.issue.ignore.multicriteria.e1.resourceKey>

		<sonar.issue.ignore.multicriteria.e2.ruleKey>java:S110</sonar.issue.ignore.multicriteria.e2.ruleKey>
		<sonar.issue.ignore.multicriteria.e2.resourceKey>**/*.java</sonar.issue.ignore.multicriteria.e2.resourceKey>
		<sonar.issue.ignore.multicriteria.e3.ruleKey>java:S4790</sonar.issue.ignore.multicriteria.e3.ruleKey>
		<sonar.issue.ignore.multicriteria.e3.resourceKey>**/*.java</sonar.issue.ignore.multicriteria.e3.resourceKey>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.kering.cus.lib</groupId>
				<artifactId>cus-lib</artifactId>
				<version>${cus-lib.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.kering.cus.lib</groupId>
				<artifactId>cus-lib-rest-provider-support</artifactId>
				<version>${cus-lib.version}</version>
			</dependency>
			<dependency>
				<groupId>com.kering.cus.lib</groupId>
				<artifactId>cus-lib-common-support</artifactId>
				<version>${cus-lib.version}</version>
			</dependency>
			<dependency>
				<groupId>com.kering.cus.lib</groupId>
				<artifactId>cus-lib-rest-consumer-support</artifactId>
				<version>${cus-lib.version}</version>
			</dependency>
			<dependency>
				<groupId>com.kering.cus.lib</groupId>
				<artifactId>cus-lib-persistence-mybatis-support</artifactId>
				<version>${cus-lib.version}</version>
			</dependency>
			<dependency>
				<groupId>com.kering.cus.lib</groupId>
				<artifactId>cus-lib-log-support</artifactId>
				<version>${cus-lib.version}</version>
			</dependency>
			<dependency>
				<groupId>com.kering.cus.lib</groupId>
				<artifactId>cus-lib-scheduler-support</artifactId>
				<version>${cus-lib.version}</version>
			</dependency>
			<dependency>
				<groupId>com.kering.cus.lib</groupId>
				<artifactId>cus-lib-secret-access-support</artifactId>
				<version>${cus-lib.version}</version>
			</dependency>
			<dependency>
				<groupId>com.kering.cus.lib</groupId>
				<artifactId>cus-lib-storage-support</artifactId>
				<version>${cus-lib.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-webflux</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-webflux</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>spring-webflux</artifactId>
					<groupId>org.springframework</groupId>
				</exclusion>
			</exclusions>
		</dependency>


		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-rest-provider-support</artifactId>
		</dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-rest-consumer-support</artifactId>
		</dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-common-support</artifactId>
		</dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-persistence-mybatis-support</artifactId>
		</dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-log-support</artifactId>
		</dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-scheduler-support</artifactId>
		</dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-secret-access-support</artifactId>
		</dependency>
		<dependency>
			<groupId>com.kering.cus.lib</groupId>
			<artifactId>cus-lib-storage-support</artifactId>
		</dependency>

		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.hibernate.validator</groupId>
			<artifactId>hibernate-validator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct-processor</artifactId>
		</dependency>

		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-common</artifactId>
			<version>4.1.115.Final</version>
		</dependency>
		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-all</artifactId>
			<version>4.1.115.Final</version>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webflux</artifactId>
			<version>6.1.15</version>
		</dependency>

		<dependency>
			<groupId>org.reflections</groupId>
			<artifactId>reflections</artifactId>
		</dependency>

		<!-- Mysql驱动包 -->
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<version>${mysql-connector.version}</version>
		</dependency>

		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>${jedis.version}</version>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
			<version>${jackson-datatype.version}</version>
		</dependency>


		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-csv</artifactId>
			<version>1.9.0</version>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.reflections</groupId>
			<artifactId>reflections</artifactId>
			<version>0.10.2</version>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<version>${mockito.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>4.1.2</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>4.1.2</version>
		</dependency>

		<dependency>
			<groupId>com.kering.icbc.sdk</groupId>
			<artifactId>icbc-api-sdk-cop</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.kering.icbc.sdk</groupId>
			<artifactId>icbc-api-sdk-cop-io</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.kering.icbc.sdk</groupId>
			<artifactId>icbc-ca</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.icbc.sdk</groupId>
			<artifactId>Java1_02_</artifactId>
			<version>jdk14</version>
		</dependency>

		<dependency>
			<groupId>com.kering.icbc.sdk.hsm</groupId>
			<artifactId>hsm-software-share</artifactId>
			<version>1.0.5</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
			<version>${spring.kafka.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>kafka-clients</artifactId>
					<groupId>org.apache.kafka</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>kafka-clients</artifactId>
			<version>${spring.kafka.client.version}</version>
		</dependency>
<!--        &lt;!&ndash;默认 4.6 版本，SelectItem 不支持泛型&ndash;&gt;-->
<!--		<dependency>-->
<!--			<groupId>com.github.jsqlparser</groupId>-->
<!--			<artifactId>jsqlparser</artifactId>-->
<!--			<version>4.9</version>-->
<!--		</dependency>-->
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>21</source>
					<target>21</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.sonarsource.scanner.maven</groupId>
				<artifactId>sonar-maven-plugin</artifactId>
				<version>${sonar-plugin.version}</version>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>${jacoco-plugin.version}</version>
				<executions>
					<execution>
						<id>prepare-jacoco</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>report</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>com.github.spotbugs</groupId>
				<artifactId>spotbugs-maven-plugin</artifactId>
				<version>${spotbugs-plugin.version}</version>
				<configuration>
					<excludeFilterFile>spotbugs-security-exclude.xml</excludeFilterFile>
					<plugins>
						<plugin>
							<groupId>com.h3xstream.findsecbugs</groupId>
							<artifactId>findsecbugs-plugin</artifactId>
							<version>${findsecbugs-plugin.version}</version>
						</plugin>
					</plugins>
				</configuration>
				<executions>
					<execution>
						<id>check</id>
						<phase>test</phase>
						<goals>
							<goal>check</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>